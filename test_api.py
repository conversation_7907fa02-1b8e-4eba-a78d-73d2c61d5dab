from dotenv import load_dotenv
import os
from openai import OpenAI

# Load environment variables
load_dotenv()

# Test OpenAI API key
api_key = os.getenv("OPENAI_API_KEY")
print(f"API Key loaded: {'Yes' if api_key else 'No'}")
if api_key:
    print(f"API Key starts with: {api_key[:10]}...")

# Test OpenAI client
try:
    client = OpenAI(api_key=api_key)
    print("OpenAI client created successfully")
    
    # Test a simple embedding
    response = client.embeddings.create(
        input="test",
        model="text-embedding-3-small"
    )
    print(f"Embedding test successful, dimension: {len(response.data[0].embedding)}")
    
except Exception as e:
    print(f"Error: {e}") 