# FAISS Multi-Cluster Setup

This document explains how to work with multiple FAISS clusters in the Knowledge Exchange Bot.

## Overview

You now have two independent FAISS clusters:

### Cluster V1 (Original)
- **Files**: `faiss.index`, `faiss_metadata.json`
- **API**: `main.py` (port 8000)
- **Test API**: `main_test.py` (port 8001)
- **Status**: ✅ Operational (untouched)

### Cluster V2 (New)
- **Files**: `faiss_v2.index`, `faiss_v2_metadata.json`
- **API**: `main_v2.py` (port 8002)
- **Test API**: `main_v2_test.py` (port 8003)
- **Status**: 🔄 Ready to create

## Quick Start

### 1. Create Cluster V2
```bash
# Create the new FAISS cluster
python3 3-embed_using_FAISS_v2.py
```

### 2. Test Cluster V2 (without OpenAI)
```bash
# Start test server
python3 main_v2_test.py

# Test in browser: http://localhost:8003
# Health check: http://localhost:8003/health
# Cluster info: http://localhost:8003/cluster-info
```

### 3. Run Full Cluster V2 API
```bash
# Start production server (requires OpenAI API key)
python3 main_v2.py

# API available at: http://localhost:8002
```

## Cluster Management

### Using the Cluster Manager
```bash
# Make executable
chmod +x cluster_manager.py

# List all clusters
python3 cluster_manager.py list

# Compare clusters
python3 cluster_manager.py compare

# Get cluster info
python3 cluster_manager.py info --cluster v1
python3 cluster_manager.py info --cluster v2

# Backup a cluster
python3 cluster_manager.py backup --cluster v1

# Delete a cluster (with confirmation)
python3 cluster_manager.py delete --cluster v2 --confirm
```

## API Endpoints

### Cluster V1 (Original)
- **Base URL**: `http://localhost:8000`
- **Search**: `POST /search`
- **Health**: `GET /health`

### Cluster V2 (New)
- **Base URL**: `http://localhost:8002`
- **Search**: `POST /search`
- **Health**: `GET /health`
- **Cluster Info**: `GET /cluster-info`

### Test APIs
- **V1 Test**: `http://localhost:8001`
- **V2 Test**: `http://localhost:8003`
- **Compare**: `GET /compare-clusters` (V2 test only)

## File Structure

```
KnowledgeExchangeBot/
├── # Original Cluster V1
├── 2-embed_using_FAISS.py      # V1 embedding script
├── main.py                     # V1 API server
├── main_test.py               # V1 test server
├── faiss.index                # V1 FAISS index
├── faiss_metadata.json        # V1 metadata
├── 
├── # New Cluster V2
├── 3-embed_using_FAISS_v2.py  # V2 embedding script
├── main_v2.py                 # V2 API server
├── main_v2_test.py            # V2 test server
├── faiss_v2.index             # V2 FAISS index (created after running script)
├── faiss_v2_metadata.json     # V2 metadata (created after running script)
├── 
├── # Shared
├── onenote_chunks.json        # Shared OneNote data
├── cluster_manager.py         # Cluster management utility
└── CLUSTER_SETUP.md          # This documentation
```

## Configuration

Both clusters use the same configuration:
- **Embedding Model**: `text-embedding-3-small`
- **Index Type**: `IndexFlatL2` (exact search)
- **Dimension**: 1536
- **Data Source**: Same OneNote chunks

## Safety Features

1. **Independent Files**: Each cluster uses separate index and metadata files
2. **No Interference**: V2 creation doesn't touch V1 files
3. **Backup Support**: Built-in backup functionality
4. **Test Mode**: Test APIs work without OpenAI API key

## Next Steps

1. **Create V2**: Run `python3 3-embed_using_FAISS_v2.py`
2. **Test V2**: Start `main_v2_test.py` and verify functionality
3. **Compare**: Use cluster manager to compare V1 and V2
4. **Deploy V2**: Start `main_v2.py` for production use

## Troubleshooting

### Common Issues

1. **OpenAI API Key**: Ensure `.env` file has `OPENAI_API_KEY`
2. **Port Conflicts**: Each API runs on different ports
3. **File Permissions**: Make sure scripts are executable
4. **Dependencies**: Ensure all packages in `requirements.txt` are installed

### Verification Commands

```bash
# Check if V1 cluster exists
ls -la faiss.index faiss_metadata.json

# Check if V2 cluster exists
ls -la faiss_v2.index faiss_v2_metadata.json

# Compare cluster sizes
python3 cluster_manager.py compare

# Test V2 without OpenAI
curl http://localhost:8003/health
```

## Support

If you encounter issues:
1. Check the cluster manager output: `python3 cluster_manager.py list`
2. Verify file permissions and existence
3. Check API logs for error messages
4. Ensure OpenAI API key is properly configured
