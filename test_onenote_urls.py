import msal
import os
import requests
import json

# === CONFIG ===
client_id = "f1547c8c-ac20-429e-bb35-bf70461505d6"
tenant_id = "25c282df-1bd0-4ae9-915e-ea3003bdd835"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scopes = ["Sites.Read.All", "Notes.Read.All"]
SITE_ID = "creospaninc.sharepoint.com,e403106b-31d0-4181-b487-2ef10c039907,c3316d29-5ff0-4cbb-965a-a57f407cd771"

# === TOKEN CACHE ===
cache_file = "msal_cache.json"
token_cache = msal.SerializableTokenCache()
if os.path.exists(cache_file):
    token_cache.deserialize(open(cache_file, "r").read())

app = msal.PublicClientApplication(client_id=client_id, authority=authority, token_cache=token_cache)
accounts = app.get_accounts()
if accounts:
    result = app.acquire_token_silent(scopes, account=accounts[0])
else:
    result = app.acquire_token_interactive(scopes)

with open(cache_file, "w") as f:
    f.write(token_cache.serialize())

if "access_token" not in result:
    print("❌ Auth failed")
    exit(1)

headers = {"Authorization": f"Bearer {result['access_token']}"}

# === LOAD NOTEBOOKS ===
notebooks_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks"
notebooks_resp = requests.get(notebooks_url, headers=headers)
notebooks = notebooks_resp.json().get("value", []) if notebooks_resp.ok else []

if not notebooks:
    print("❌ No notebooks found.")
    exit(0)

# === TEST SPECIFIC SECTION ===
# Look for n8n section in Low code no code section group
target_notebook = None
target_section = None
target_section_group = None

for nb in notebooks:
    print(f"\n📓 Notebook: {nb['displayName']}")
    
    # Check section groups for "Low code no code"
    sg_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/notebooks/{nb['id']}/sectionGroups"
    sg_resp = requests.get(sg_url, headers=headers)
    
    for group in sg_resp.json().get("value", []):
        print(f"  📁 Section Group: {group['displayName']}")
        
        if group['displayName'].lower() == "low code no code":
            target_section_group = group
            target_notebook = nb
            
            # Get sections in this group
            group_sections_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sectionGroups/{group['id']}/sections"
            group_sections_resp = requests.get(group_sections_url, headers=headers)
            
            for section in group_sections_resp.json().get("value", []):
                print(f"    📑 Section: {section['displayName']}")
                
                if "n8n" in section['displayName'].lower():
                    target_section = section
                    break
            
            if target_section:
                break
    
    if target_section:
        break

if not target_section:
    print("❌ Could not find n8n section in Low code no code group")
    exit(0)

# === GET PAGES AND URLS ===
print(f"\n✅ Found section: {target_section['displayName']} in group: {target_section_group['displayName']}")

pages_url = f"https://graph.microsoft.com/v1.0/sites/{SITE_ID}/onenote/sections/{target_section['id']}/pages"
pages_resp = requests.get(pages_url, headers=headers)
pages = pages_resp.json().get("value", []) if pages_resp.ok else []

print("\n=== URL COMPARISON ===")
for page in pages:
    # Get the URL from the API
    api_url = page.get("links", {}).get("oneNoteWebUrl", {}).get("href", "")
    
    # Extract the target part
    target_part = ""
    if "wd=target" in api_url:
        target_part = api_url.split("wd=target")[1]
    
    # Construct the expected SharePoint URL
    expected_url = f"https://creospaninc.sharepoint.com/sites/CreospanKnowledgeExchange/_layouts/15/Doc.aspx?sourcedoc={{37a96d0e-c291-4cbe-8713-d163908468bb}}&action=edit&wd=target{target_part}&wdorigin=NavigationUrl"
    
    print(f"\n📄 Page: {page['title']}")
    print(f"API URL: {api_url}")
    print(f"Expected URL: {expected_url}")
    print(f"URL Match: {'✅' if api_url == expected_url else '❌'}")

# Save a sample of the API response for further analysis
with open("api_response_sample.json", "w", encoding="utf-8") as f:
    json.dump(pages[0] if pages else {}, f, indent=2, ensure_ascii=False)

print(f"\n✅ Saved API response sample to api_response_sample.json")