from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import faiss
import numpy as np
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# === CONFIG ===
EMBEDDING_MODEL = "text-embedding-3-small"
INDEX_FILE = "faiss.index"
METADATA_FILE = "faiss_metadata.json"
CHUNKS_FILE = "onenote_chunks.json"

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True

# === LOAD DATA (without OpenAI for testing) ===
try:
    index = faiss.read_index(INDEX_FILE)
    print(f"✅ FAISS index loaded successfully with {index.ntotal} vectors")
except Exception as e:
    print(f"❌ Error loading FAISS index: {e}")
    index = None

try:
    with open(METADATA_FILE, "r", encoding="utf-8") as f:
        metadata = json.load(f)
    print(f"✅ Metadata loaded successfully with {len(metadata)} entries")
except Exception as e:
    print(f"❌ Error loading metadata: {e}")
    metadata = []

try:
    with open(CHUNKS_FILE, "r", encoding="utf-8") as f:
        full_chunks = json.load(f)
    print(f"✅ Chunks loaded successfully with {len(full_chunks)} chunks")
except Exception as e:
    print(f"❌ Error loading chunks: {e}")
    full_chunks = []

# === FASTAPI SETUP ===
app = FastAPI(title="Knowledge Exchange Bot", description="Search your OneNote content")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {
        "status": "ok",
        "message": "Knowledge Exchange Bot API is running",
        "data_status": {
            "faiss_index": "loaded" if index else "error",
            "metadata": f"{len(metadata)} entries" if metadata else "error",
            "chunks": f"{len(full_chunks)} chunks" if full_chunks else "error"
        }
    }

@app.post("/search")
def search(req: SearchRequest):
    if not index or not full_chunks:
        return {
            "error": "Data not properly loaded",
            "query": req.query,
            "results": [],
            "answer": "System not ready - please check data files"
        }
    
    # For testing purposes, return first few chunks instead of doing semantic search
    results = []
    context_chunks = []
    
    # Take the first top_k chunks as a test
    for i in range(min(req.top_k, len(full_chunks))):
        chunk = full_chunks[i]
        result = {
            "notebook": chunk["metadata"]["notebook"],
            "section": chunk["metadata"]["section"],
            "page": chunk["metadata"]["page"],
            "chunk_index": chunk["metadata"]["chunk_index"],
            "page_url": chunk["metadata"].get("page_url"),
            "text": chunk["text"][:200] + "..." if len(chunk["text"]) > 200 else chunk["text"]
        }
        context_chunks.append(chunk["text"])
        results.append(result)

    return {
        "query": req.query,
        "results": results,
        "answer": f"Found {len(results)} results for '{req.query}' (test mode - OpenAI integration disabled)",
        "note": "This is test mode. Update OPENAI_API_KEY in .env for full functionality"
    }

@app.get("/health")
def health():
    return {
        "status": "healthy",
        "components": {
            "faiss": "ok" if index else "error",
            "metadata": "ok" if metadata else "error", 
            "chunks": "ok" if full_chunks else "error"
        }
    }

# Optional: run locally
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_test:app", host="127.0.0.1", port=8001, reload=True) 