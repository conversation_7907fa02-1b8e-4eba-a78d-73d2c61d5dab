import json
import os
import faiss
import numpy as np
from tqdm import tqdm
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# === CONFIGURATION FOR CLUSTER V2 ===
EMBEDDING_MODEL = "text-embedding-3-small"
CHUNK_FILE = "onenote_chunks.json"
INDEX_FILE = "faiss_v2.index"
METADATA_FILE = "faiss_v2_metadata.json"

# === OPENAI SETUP ===
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# === LOAD CHUNKS ===
with open(CHUNK_FILE, "r", encoding="utf-8") as f:
    chunks = json.load(f)

print(f"📦 Loaded {len(chunks)} chunks from {CHUNK_FILE}")
print(f"🔄 Creating FAISS Cluster V2...")

# === EMBED & INDEX ===
embeddings = []
metadata = []

for chunk in tqdm(chunks, desc="🔁 Embedding for Cluster V2"):
    try:
        text = chunk["text"].replace("\n", " ").strip()
        if not text:
            continue

        response = client.embeddings.create(
            input=text,
            model=EMBEDDING_MODEL
        )
        embedding = response.data[0].embedding
        embeddings.append(embedding)

        # Preserve full metadata (for traceability)
        metadata.append(chunk["metadata"])

    except Exception as e:
        print("❌ Error embedding chunk:", e)

# === SAFETY CHECK ===
if not embeddings:
    raise RuntimeError("❌ No embeddings created. Check OpenAI credentials or input format.")

# === FAISS SETUP FOR V2 ===
np_embeddings = np.array(embeddings).astype("float32")
dimension = len(np_embeddings[0])

# Using IndexFlatL2 for exact search (same as original cluster)
index = faiss.IndexFlatL2(dimension)
index.add(np_embeddings)

print(f"✅ FAISS Cluster V2 created with {index.ntotal} vectors")

# === SAVE ARTIFACTS FOR V2 ===
faiss.write_index(index, INDEX_FILE)
print(f"💾 FAISS Cluster V2 index saved to {INDEX_FILE}")

with open(METADATA_FILE, "w", encoding="utf-8") as f:
    json.dump(metadata, f, indent=2, ensure_ascii=False)
print(f"💾 Cluster V2 metadata saved to {METADATA_FILE}")

print(f"🎉 FAISS Cluster V2 setup complete!")
print(f"📊 Cluster V2 Stats:")
print(f"   - Vectors: {index.ntotal}")
print(f"   - Dimension: {dimension}")
print(f"   - Index Type: IndexFlatL2")
print(f"   - Files: {INDEX_FILE}, {METADATA_FILE}")
