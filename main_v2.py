from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from openai import OpenAI
import faiss
import numpy as np
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# === CONFIG FOR CLUSTER V2 ===
EMBEDDING_MODEL = "text-embedding-3-small"
INDEX_FILE = "faiss_v2.index"
METADATA_FILE = "faiss_v2_metadata.json"
CHUNKS_FILE = "onenote_chunks.json"

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True

# === INIT OPENAI & LOAD DATA FOR CLUSTER V2 ===
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Load FAISS Cluster V2
try:
    index = faiss.read_index(INDEX_FILE)
    print(f"✅ FAISS Cluster V2 loaded successfully with {index.ntotal} vectors")
except Exception as e:
    print(f"❌ Error loading FAISS Cluster V2: {e}")
    index = None

# Load metadata for Cluster V2
try:
    with open(METADATA_FILE, "r", encoding="utf-8") as f:
        metadata = json.load(f)
    print(f"✅ Cluster V2 metadata loaded: {len(metadata)} entries")
except Exception as e:
    print(f"❌ Error loading Cluster V2 metadata: {e}")
    metadata = []

# Load full chunks
try:
    with open(CHUNKS_FILE, "r", encoding="utf-8") as f:
        full_chunks = json.load(f)
    print(f"✅ Full chunks loaded: {len(full_chunks)} entries")
except Exception as e:
    print(f"❌ Error loading chunks: {e}")
    full_chunks = []

# === FASTAPI SETUP ===
app = FastAPI(title="Knowledge Exchange Bot - Cluster V2", version="2.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {
        "status": "ok",
        "cluster": "v2",
        "description": "Knowledge Exchange Bot - FAISS Cluster V2"
    }

@app.post("/search")
def search(req: SearchRequest):
    if not index:
        return {"error": "FAISS Cluster V2 not loaded"}
    
    # Embed the query
    response = client.embeddings.create(
        input=req.query,
        model=EMBEDDING_MODEL
    )
    query_vector = np.array([response.data[0].embedding], dtype="float32")

    # Perform FAISS search on Cluster V2
    D, I = index.search(query_vector, req.top_k)

    results = []
    context_chunks = []

    for idx in I[0]:
        if idx < len(full_chunks):
            chunk = full_chunks[idx]
            result = {
                "notebook": chunk["metadata"]["notebook"],
                "section": chunk["metadata"]["section"],
                "page": chunk["metadata"]["page"],
                "chunk_index": chunk["metadata"]["chunk_index"],
                "page_url": chunk["metadata"].get("page_url"),
                "text": chunk["text"],
                "cluster": "v2"
            }
            context_chunks.append(chunk["text"])
            results.append(result)

    # Generate answer if requested
    answer = None
    if req.generate_answer and context_chunks:
        context = "\n\n".join(context_chunks)
        
        try:
            completion = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that answers questions based on the provided context from OneNote knowledge base. Use only the information provided in the context."
                    },
                    {
                        "role": "user",
                        "content": f"Context:\n{context}\n\nQuestion: {req.query}\n\nAnswer:"
                    }
                ],
                max_tokens=500,
                temperature=0.1
            )
            answer = completion.choices[0].message.content
        except Exception as e:
            answer = f"Error generating answer: {str(e)}"

    return {
        "query": req.query,
        "results": results,
        "answer": answer,
        "cluster": "v2",
        "total_vectors": index.ntotal if index else 0
    }

@app.get("/health")
def health():
    return {
        "status": "healthy",
        "cluster": "v2",
        "components": {
            "faiss": "ok" if index else "error",
            "metadata": "ok" if metadata else "error", 
            "chunks": "ok" if full_chunks else "error"
        },
        "stats": {
            "total_vectors": index.ntotal if index else 0,
            "metadata_entries": len(metadata),
            "chunk_entries": len(full_chunks)
        }
    }

@app.get("/cluster-info")
def cluster_info():
    return {
        "cluster_version": "v2",
        "index_file": INDEX_FILE,
        "metadata_file": METADATA_FILE,
        "chunks_file": CHUNKS_FILE,
        "embedding_model": EMBEDDING_MODEL,
        "total_vectors": index.ntotal if index else 0,
        "dimension": index.d if index else 0,
        "index_type": "IndexFlatL2"
    }

# Optional: run locally
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_v2:app", host="127.0.0.1", port=8002, reload=True)
