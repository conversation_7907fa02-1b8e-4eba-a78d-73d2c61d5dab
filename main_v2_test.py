from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import faiss
import numpy as np
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# === CONFIG FOR CLUSTER V2 TEST ===
EMBEDDING_MODEL = "text-embedding-3-small"
INDEX_FILE = "faiss_v2.index"
METADATA_FILE = "faiss_v2_metadata.json"
CHUNKS_FILE = "onenote_chunks.json"

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True

# === LOAD DATA (without OpenAI for testing) ===
try:
    index = faiss.read_index(INDEX_FILE)
    print(f"✅ FAISS Cluster V2 loaded successfully with {index.ntotal} vectors")
except Exception as e:
    print(f"❌ Error loading FAISS Cluster V2: {e}")
    index = None

try:
    with open(METADATA_FILE, "r", encoding="utf-8") as f:
        metadata = json.load(f)
    print(f"✅ Cluster V2 metadata loaded: {len(metadata)} entries")
except Exception as e:
    print(f"❌ Error loading Cluster V2 metadata: {e}")
    metadata = []

try:
    with open(CHUNKS_FILE, "r", encoding="utf-8") as f:
        full_chunks = json.load(f)
    print(f"✅ Full chunks loaded: {len(full_chunks)} entries")
except Exception as e:
    print(f"❌ Error loading chunks: {e}")
    full_chunks = []

# === FASTAPI SETUP ===
app = FastAPI(title="Knowledge Exchange Bot - Cluster V2 Test", version="2.0.0-test")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {
        "status": "ok",
        "cluster": "v2",
        "mode": "test",
        "description": "Knowledge Exchange Bot - FAISS Cluster V2 (Test Mode)"
    }

@app.post("/search")
def search(req: SearchRequest):
    if not index:
        return {"error": "FAISS Cluster V2 not loaded"}
    
    # For test mode, create a dummy query vector (normally would use OpenAI)
    # Using random vector for demonstration
    query_vector = np.random.rand(1, index.d).astype("float32")
    
    # Perform FAISS search on Cluster V2
    D, I = index.search(query_vector, req.top_k)

    results = []
    
    for idx in I[0]:
        if idx < len(full_chunks):
            chunk = full_chunks[idx]
            result = {
                "notebook": chunk["metadata"]["notebook"],
                "section": chunk["metadata"]["section"],
                "page": chunk["metadata"]["page"],
                "chunk_index": chunk["metadata"]["chunk_index"],
                "page_url": chunk["metadata"].get("page_url"),
                "text": chunk["text"][:200] + "..." if len(chunk["text"]) > 200 else chunk["text"],
                "cluster": "v2",
                "distance": float(D[0][len(results)])
            }
            results.append(result)

    return {
        "query": req.query,
        "results": results,
        "answer": f"Found {len(results)} results for '{req.query}' from Cluster V2 (test mode - OpenAI integration disabled)",
        "note": "This is test mode. Update OPENAI_API_KEY in .env for full functionality",
        "cluster": "v2",
        "total_vectors": index.ntotal if index else 0
    }

@app.get("/health")
def health():
    return {
        "status": "healthy",
        "cluster": "v2",
        "mode": "test",
        "components": {
            "faiss": "ok" if index else "error",
            "metadata": "ok" if metadata else "error", 
            "chunks": "ok" if full_chunks else "error"
        },
        "stats": {
            "total_vectors": index.ntotal if index else 0,
            "metadata_entries": len(metadata),
            "chunk_entries": len(full_chunks)
        }
    }

@app.get("/cluster-info")
def cluster_info():
    return {
        "cluster_version": "v2",
        "mode": "test",
        "index_file": INDEX_FILE,
        "metadata_file": METADATA_FILE,
        "chunks_file": CHUNKS_FILE,
        "embedding_model": EMBEDDING_MODEL,
        "total_vectors": index.ntotal if index else 0,
        "dimension": index.d if index else 0,
        "index_type": "IndexFlatL2"
    }

@app.get("/compare-clusters")
def compare_clusters():
    """Compare V1 and V2 clusters"""
    v1_stats = {}
    v2_stats = {}
    
    # Try to load V1 cluster info
    try:
        v1_index = faiss.read_index("faiss.index")
        v1_stats = {
            "total_vectors": v1_index.ntotal,
            "dimension": v1_index.d,
            "status": "loaded"
        }
    except Exception as e:
        v1_stats = {"status": "error", "error": str(e)}
    
    # V2 cluster info
    if index:
        v2_stats = {
            "total_vectors": index.ntotal,
            "dimension": index.d,
            "status": "loaded"
        }
    else:
        v2_stats = {"status": "error", "error": "V2 index not loaded"}
    
    return {
        "cluster_v1": v1_stats,
        "cluster_v2": v2_stats,
        "comparison": {
            "both_loaded": v1_stats.get("status") == "loaded" and v2_stats.get("status") == "loaded",
            "same_dimension": v1_stats.get("dimension") == v2_stats.get("dimension"),
            "vector_count_difference": v2_stats.get("total_vectors", 0) - v1_stats.get("total_vectors", 0)
        }
    }

# Optional: run locally
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_v2_test:app", host="127.0.0.1", port=8003, reload=True)
