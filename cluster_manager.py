#!/usr/bin/env python3
"""
FAISS Cluster Manager
Utility to manage multiple FAISS clusters for the Knowledge Exchange Bot
"""

import os
import json
import faiss
import argparse
from datetime import datetime

class FAISSClusterManager:
    def __init__(self):
        self.clusters = {
            "v1": {
                "index_file": "faiss.index",
                "metadata_file": "faiss_metadata.json",
                "description": "Original FAISS cluster"
            },
            "v2": {
                "index_file": "faiss_v2.index",
                "metadata_file": "faiss_v2_metadata.json",
                "description": "Second FAISS cluster"
            }
        }
    
    def list_clusters(self):
        """List all available clusters with their status"""
        print("📊 FAISS Cluster Status:")
        print("=" * 50)
        
        for cluster_name, config in self.clusters.items():
            print(f"\n🔹 Cluster {cluster_name.upper()}:")
            print(f"   Description: {config['description']}")
            print(f"   Index File: {config['index_file']}")
            print(f"   Metadata File: {config['metadata_file']}")
            
            # Check if files exist
            index_exists = os.path.exists(config['index_file'])
            metadata_exists = os.path.exists(config['metadata_file'])
            
            print(f"   Index Status: {'✅ EXISTS' if index_exists else '❌ MISSING'}")
            print(f"   Metadata Status: {'✅ EXISTS' if metadata_exists else '❌ MISSING'}")
            
            if index_exists:
                try:
                    index = faiss.read_index(config['index_file'])
                    print(f"   Vectors: {index.ntotal}")
                    print(f"   Dimension: {index.d}")
                    print(f"   Index Type: {type(index).__name__}")
                except Exception as e:
                    print(f"   ❌ Error reading index: {e}")
            
            if metadata_exists:
                try:
                    with open(config['metadata_file'], 'r') as f:
                        metadata = json.load(f)
                    print(f"   Metadata Entries: {len(metadata)}")
                except Exception as e:
                    print(f"   ❌ Error reading metadata: {e}")
    
    def compare_clusters(self):
        """Compare all clusters"""
        print("🔍 Cluster Comparison:")
        print("=" * 50)
        
        cluster_stats = {}
        
        for cluster_name, config in self.clusters.items():
            stats = {"name": cluster_name}
            
            try:
                if os.path.exists(config['index_file']):
                    index = faiss.read_index(config['index_file'])
                    stats.update({
                        "vectors": index.ntotal,
                        "dimension": index.d,
                        "index_type": type(index).__name__,
                        "status": "loaded"
                    })
                else:
                    stats["status"] = "missing"
            except Exception as e:
                stats["status"] = f"error: {e}"
            
            cluster_stats[cluster_name] = stats
        
        # Print comparison
        for cluster_name, stats in cluster_stats.items():
            print(f"\n🔹 {cluster_name.upper()}:")
            if stats["status"] == "loaded":
                print(f"   Vectors: {stats['vectors']}")
                print(f"   Dimension: {stats['dimension']}")
                print(f"   Type: {stats['index_type']}")
            else:
                print(f"   Status: {stats['status']}")
        
        # Check for differences
        loaded_clusters = [name for name, stats in cluster_stats.items() if stats["status"] == "loaded"]
        
        if len(loaded_clusters) > 1:
            print(f"\n📈 Comparison Results:")
            dimensions = [cluster_stats[name]["dimension"] for name in loaded_clusters]
            vectors = [cluster_stats[name]["vectors"] for name in loaded_clusters]
            
            print(f"   Same Dimension: {'✅ YES' if len(set(dimensions)) == 1 else '❌ NO'}")
            print(f"   Vector Counts: {dict(zip(loaded_clusters, vectors))}")
    
    def backup_cluster(self, cluster_name):
        """Create a backup of a cluster"""
        if cluster_name not in self.clusters:
            print(f"❌ Unknown cluster: {cluster_name}")
            return
        
        config = self.clusters[cluster_name]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Backup index file
        if os.path.exists(config['index_file']):
            backup_index = f"{config['index_file']}.backup_{timestamp}"
            os.system(f"cp {config['index_file']} {backup_index}")
            print(f"✅ Index backed up to: {backup_index}")
        
        # Backup metadata file
        if os.path.exists(config['metadata_file']):
            backup_metadata = f"{config['metadata_file']}.backup_{timestamp}"
            os.system(f"cp {config['metadata_file']} {backup_metadata}")
            print(f"✅ Metadata backed up to: {backup_metadata}")
    
    def delete_cluster(self, cluster_name, confirm=False):
        """Delete a cluster (with confirmation)"""
        if cluster_name not in self.clusters:
            print(f"❌ Unknown cluster: {cluster_name}")
            return
        
        if not confirm:
            print(f"⚠️  This will delete cluster {cluster_name}!")
            print("   Use --confirm flag to proceed")
            return
        
        config = self.clusters[cluster_name]
        
        # Delete files
        for file_path in [config['index_file'], config['metadata_file']]:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🗑️  Deleted: {file_path}")
            else:
                print(f"⚠️  File not found: {file_path}")
    
    def cluster_info(self, cluster_name):
        """Get detailed info about a specific cluster"""
        if cluster_name not in self.clusters:
            print(f"❌ Unknown cluster: {cluster_name}")
            return
        
        config = self.clusters[cluster_name]
        print(f"📋 Cluster {cluster_name.upper()} Details:")
        print("=" * 40)
        
        for key, value in config.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        
        # File details
        for file_type, file_path in [("Index", config['index_file']), ("Metadata", config['metadata_file'])]:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   {file_type} Size: {size:,} bytes ({size/1024/1024:.2f} MB)")
                print(f"   {file_type} Modified: {datetime.fromtimestamp(os.path.getmtime(file_path))}")

def main():
    parser = argparse.ArgumentParser(description="FAISS Cluster Manager")
    parser.add_argument("command", choices=["list", "compare", "backup", "delete", "info"], 
                       help="Command to execute")
    parser.add_argument("--cluster", help="Cluster name (for backup, delete, info commands)")
    parser.add_argument("--confirm", action="store_true", help="Confirm destructive operations")
    
    args = parser.parse_args()
    
    manager = FAISSClusterManager()
    
    if args.command == "list":
        manager.list_clusters()
    elif args.command == "compare":
        manager.compare_clusters()
    elif args.command == "backup":
        if not args.cluster:
            print("❌ --cluster required for backup command")
            return
        manager.backup_cluster(args.cluster)
    elif args.command == "delete":
        if not args.cluster:
            print("❌ --cluster required for delete command")
            return
        manager.delete_cluster(args.cluster, args.confirm)
    elif args.command == "info":
        if not args.cluster:
            print("❌ --cluster required for info command")
            return
        manager.cluster_info(args.cluster)

if __name__ == "__main__":
    main()
